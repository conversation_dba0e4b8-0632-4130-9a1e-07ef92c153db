import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Modal,
  Drawer,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Pagination,
  Tag,
} from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FilterOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import type { TableRowSelection } from "antd/es/table/interface";
import type {
  TaskData,
  TaskFormData,
  TaskSearchParams,
  TaskSelectionState,
  TaskModalState,
  TaskDrawerState,
} from "../types/task";
import { TaskService } from "../services/taskService";
import {
  TASK_STATUS_OPTIONS,
  WEEKDAY_OPTIONS,
  FREQUENCY_OPTIONS,
} from "../types/task";
import "./AntdTable.module.css";

const { Option } = Select;

/**
 * 任务管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AntdTable: React.FC = () => {
  // 表格数据状态
  const [data, setData] = useState<TaskData[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<TaskSearchParams>({});

  // 表格选择状态
  const [selection, setSelection] = useState<TaskSelectionState>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // Modal状态
  const [addModal, setAddModal] = useState<TaskModalState>({ visible: false });
  const [searchModal, setSearchModal] = useState<TaskModalState>({
    visible: false,
  });

  // 抽屉状态
  const [editDrawer, setEditDrawer] = useState<TaskDrawerState>({
    visible: false,
  });
  const [currentRecord, setCurrentRecord] = useState<TaskData | null>(null);

  // 表单实例
  const [searchForm] = Form.useForm();
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载数据
  const loadData = useCallback(
    async (customParams?: Partial<TaskSearchParams>) => {
      setLoading(true);
      try {
        const params = {
          ...searchParams,
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...customParams,
        };
        const response = await TaskService.getTasks(params);
        setData(response.data);
        setTotal(response.total);
      } catch (error) {
        message.error("加载数据失败");
        console.error("加载数据失败:", error);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, pagination]
  );

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 搜索处理
  const handleSearch = useCallback(() => {
    setPagination((prev) => ({ ...prev, current: 1 }));
    loadData();
  }, [loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchParams({});
    setPagination({ current: 1, pageSize: 10 });
    // 触发数据重新加载
    setTimeout(() => {
      loadData();
    }, 0);
  }, [loadData]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskSearchParams) => {
      setSearchParams(values);
      setPagination((prev) => ({ ...prev, current: 1 }));
      setSearchModal({ visible: false });
      // 触发数据重新加载
      setTimeout(() => {
        loadData();
      }, 0);
    },
    [loadData]
  );

  // 表格列定义
  const columns: TableColumnsType<TaskData> = [
    {
      title: "任务名称",
      dataIndex: "name",
      key: "name",
      width: 180,
      ellipsis: true,
      fixed: "left",
    },
    {
      title: "任务分组",
      dataIndex: "group",
      key: "group",
      width: 120,
    },
    {
      title: "执行时间",
      key: "time",
      width: 180,
      render: (_, record) => (
        <span>
          {record.start_time} - {record.end_time}
        </span>
      ),
    },
    {
      title: "星期",
      dataIndex: "weekday",
      key: "weekday",
      width: 100,
    },
    {
      title: "执行频率",
      dataIndex: "frequency",
      key: "frequency",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          active: {
            color: "success",
            text: "激活",
            className: "bg-green-100 text-green-800 border-green-200",
          },
          inactive: {
            color: "error",
            text: "未激活",
            className: "bg-red-100 text-red-800 border-red-200",
          },
          pending: {
            color: "warning",
            text: "待处理",
            className: "bg-yellow-100 text-yellow-800 border-yellow-200",
          },
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Tag
            color={config?.color}
            className={`${config?.className} font-medium rounded-full px-3 py-1`}
          >
            {config?.text}
          </Tag>
        );
      },
    },
    {
      title: "重试次数",
      dataIndex: "retryNum",
      key: "retryNum",
      width: 100,
    },
    {
      title: "告警接收人",
      dataIndex: "alert_receiver",
      key: "alert_receiver",
      width: 150,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
    },
    {
      title: "操作",
      key: "action",
      width: 140,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              editForm.setFieldsValue(record);
              setEditDrawer({ visible: true });
            }}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md px-2 py-1 transition-all duration-200"
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个任务吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            placement="topRight"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md px-2 py-1 transition-all duration-200"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 表格行选择配置
  const rowSelection: TableRowSelection<TaskData> = {
    selectedRowKeys: selection.selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskData[]) => {
      setSelection({ selectedRowKeys, selectedRows });
    },
  };

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        await TaskService.deleteTask(id);
        message.success("删除成功");
        loadData();
      } catch (error) {
        message.error("删除失败");
        console.error("删除失败:", error);
      }
    },
    [loadData]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    if (selection.selectedRowKeys.length === 0) {
      message.warning("请先选择要删除的任务");
      return;
    }

    Modal.confirm({
      title: "确认批量删除",
      content: `确定要删除选中的 ${selection.selectedRowKeys.length} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        try {
          const ids = selection.selectedRowKeys.map((key) => Number(key));
          await TaskService.batchDeleteTasks(ids);
          message.success("批量删除成功");
          setSelection({ selectedRowKeys: [], selectedRows: [] });
          loadData();
        } catch (error) {
          message.error("批量删除失败");
          console.error("批量删除失败:", error);
        }
      },
    });
  }, [selection.selectedRowKeys, loadData]);

  // 编辑任务提交
  const handleEditSubmit = useCallback(
    async (values: TaskFormData) => {
      if (!currentRecord) return;

      try {
        setEditDrawer((prev) => ({ ...prev, loading: true }));
        await TaskService.updateTask(currentRecord.id, values);
        message.success("更新成功");
        setEditDrawer({ visible: false, loading: false });
        setCurrentRecord(null);
        editForm.resetFields();
        loadData();
      } catch (error) {
        message.error("更新失败");
        console.error("更新失败:", error);
        setEditDrawer((prev) => ({ ...prev, loading: false }));
      }
    },
    [currentRecord, editForm, loadData]
  );

  // 抽屉关闭处理（带确认）
  const handleDrawerClose = useCallback(() => {
    if (editForm.isFieldsTouched()) {
      Modal.confirm({
        title: "确认关闭",
        content: "表单内容已修改，确定要关闭吗？",
        icon: <ExclamationCircleOutlined />,
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          setEditDrawer({ visible: false });
          setCurrentRecord(null);
          editForm.resetFields();
        },
      });
    } else {
      setEditDrawer({ visible: false });
      setCurrentRecord(null);
      editForm.resetFields();
    }
  }, [editForm]);

  // 新增任务提交
  const handleAddSubmit = useCallback(
    async (values: TaskFormData) => {
      try {
        setAddModal((prev) => ({ ...prev, loading: true }));
        await TaskService.addTask(values);
        message.success("新增成功");
        setAddModal({ visible: false, loading: false });
        addForm.resetFields();
        loadData();
      } catch (error) {
        message.error("新增失败");
        console.error("新增失败:", error);
        setAddModal((prev) => ({ ...prev, loading: false }));
      }
    },
    [addForm, loadData]
  );

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100 relative">
      {/* 查询区域 - 固定在顶部 */}
      <div className="bg-white shadow-lg border-b border-gray-200 p-6 flex-shrink-0">
        <div className="max-w-7xl mx-auto">
          {/* 快速搜索区域 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <Row gutter={[16, 16]} className="mb-4">
              <Col xs={24} sm={12} md={6}>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700">
                    任务名称
                  </label>
                  <Input
                    placeholder="请输入任务名称"
                    prefix={<SearchOutlined className="text-gray-400" />}
                    allowClear
                    className="rounded-md"
                    onChange={(e) => {
                      setSearchParams((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }));
                    }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700">
                    任务分组
                  </label>
                  <Select
                    placeholder="请选择任务分组"
                    allowClear
                    className="w-full rounded-md"
                    onChange={(value) => {
                      setSearchParams((prev) => ({ ...prev, group: value }));
                    }}
                  >
                    <Option value="系统维护">系统维护</Option>
                    <Option value="数据备份">数据备份</Option>
                    <Option value="监控告警">监控告警</Option>
                    <Option value="日志清理">日志清理</Option>
                    <Option value="性能优化">性能优化</Option>
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700">
                    任务状态
                  </label>
                  <Select
                    placeholder="请选择任务状态"
                    allowClear
                    className="w-full rounded-md"
                    onChange={(value) => {
                      setSearchParams((prev) => ({ ...prev, status: value }));
                    }}
                  >
                    {TASK_STATUS_OPTIONS.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700">
                    操作
                  </label>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      type="primary"
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                      className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md"
                    >
                      搜索
                    </Button>
                    <Button onClick={handleReset} className="rounded-md">
                      重置
                    </Button>
                    <Button
                      icon={<FilterOutlined />}
                      onClick={() => setSearchModal({ visible: true })}
                      className="rounded-md"
                    >
                      详细查询
                    </Button>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setAddModal({ visible: true })}
                      className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 rounded-md"
                    >
                      新增任务
                    </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </div>

      {/* 批量操作栏 - 当有选中项时显示，绝对定位覆盖在查询区域之上 */}
      {selection.selectedRowKeys.length > 0 && (
        <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-4 z-30 shadow-lg backdrop-blur-sm">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-blue-700 font-medium">
                已选择 {selection.selectedRowKeys.length} 项
              </span>
            </div>
            <Space>
              <Button
                type="link"
                danger
                onClick={handleBatchDelete}
                className="hover:bg-red-50 rounded-md px-3"
              >
                批量删除
              </Button>
              <Button
                type="link"
                onClick={() => {
                  setSelection({ selectedRowKeys: [], selectedRows: [] });
                }}
                className="hover:bg-gray-50 rounded-md px-3"
              >
                取消全选
              </Button>
            </Space>
          </div>
        </div>
      )}

      {/* 表格区域 - 中间可滚动 */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full max-w-7xl mx-auto p-6">
          <Card className="h-full shadow-lg border-0 rounded-xl overflow-hidden">
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-auto">
                <Table
                  columns={columns}
                  dataSource={data}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  rowSelection={rowSelection}
                  scroll={{ x: 1200, y: "calc(100vh - 320px)" }}
                  size="middle"
                  className="custom-table"
                />
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 分页区域 - 固定在底部 */}
      <div className="bg-white border-t border-gray-200 p-4 flex-shrink-0 shadow-lg">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-gray-600 font-medium">共 {total} 条数据</span>
          </div>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
            }
            className="custom-pagination"
            onChange={(page, pageSize) => {
              const newPagination = { current: page, pageSize: pageSize || 10 };
              setPagination(newPagination);
              // 使用新的分页参数立即加载数据
              loadData({
                current: page,
                pageSize: pageSize || 10,
              });
            }}
          />
        </div>
      </div>

      {/* 新增任务Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <PlusOutlined className="text-green-600" />
            <span className="text-lg font-semibold">新增任务</span>
          </div>
        }
        open={addModal.visible}
        onCancel={() => {
          setAddModal({ visible: false });
          addForm.resetFields();
        }}
        footer={null}
        width={800}
        className="custom-modal"
      >
        <Form form={addForm} layout="vertical" onFinish={handleAddSubmit}>
          <Form.Item
            label="任务名称"
            name="name"
            rules={[{ required: true, message: "请输入任务名称" }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item
            label="任务分组"
            name="group"
            rules={[{ required: true, message: "请选择任务分组" }]}
          >
            <Select placeholder="请选择任务分组">
              <Option value="系统维护">系统维护</Option>
              <Option value="数据备份">数据备份</Option>
              <Option value="监控告警">监控告警</Option>
              <Option value="日志清理">日志清理</Option>
              <Option value="性能优化">性能优化</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="开始时间"
                name="start_time"
                rules={[{ required: true, message: "请输入开始时间" }]}
              >
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="结束时间"
                name="end_time"
                rules={[{ required: true, message: "请输入结束时间" }]}
              >
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="星期"
                name="weekday"
                rules={[{ required: true, message: "请选择星期" }]}
              >
                <Select placeholder="请选择星期">
                  {WEEKDAY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="执行频率"
                name="frequency"
                rules={[{ required: true, message: "请选择执行频率" }]}
              >
                <Select placeholder="请选择执行频率">
                  {FREQUENCY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="重试次数"
                name="retryNum"
                rules={[{ required: true, message: "请输入重试次数" }]}
              >
                <Input placeholder="请输入重试次数" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="重试间隔"
                name="retry_frequency"
                rules={[{ required: true, message: "请输入重试间隔" }]}
              >
                <Input placeholder="请输入重试间隔" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="告警触发条件"
            name="alert_trigger"
            rules={[{ required: true, message: "请输入告警触发条件" }]}
          >
            <Select placeholder="请选择告警触发条件">
              <Option value="失败时">失败时</Option>
              <Option value="成功时">成功时</Option>
              <Option value="总是">总是</Option>
              <Option value="从不">从不</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="告警接收人"
            name="alert_receiver"
            rules={[{ required: true, message: "请输入告警接收人" }]}
          >
            <Input placeholder="请输入告警接收人" />
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {TASK_STATUS_OPTIONS.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              onClick={() => {
                setAddModal({ visible: false });
                addForm.resetFields();
              }}
              className="rounded-md px-6"
            >
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={addModal.loading}
              className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 rounded-md px-6"
            >
              保存
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 详细查询Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <FilterOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={() => setSearchModal({ visible: false })}
        footer={null}
        width={800}
        className="custom-modal"
      >
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleAdvancedSearch}
          initialValues={searchParams}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="任务名称" name="name">
                <Input placeholder="请输入任务名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="任务分组" name="group">
                <Select placeholder="请选择任务分组" allowClear>
                  <Option value="系统维护">系统维护</Option>
                  <Option value="数据备份">数据备份</Option>
                  <Option value="监控告警">监控告警</Option>
                  <Option value="日志清理">日志清理</Option>
                  <Option value="性能优化">性能优化</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="任务状态" name="status">
                <Select placeholder="请选择任务状态" allowClear>
                  {TASK_STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="执行频率" name="frequency">
                <Select placeholder="请选择执行频率" allowClear>
                  {FREQUENCY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="星期" name="weekday">
                <Select placeholder="请选择星期" allowClear>
                  {WEEKDAY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="告警接收人" name="alert_receiver">
                <Input placeholder="请输入告警接收人" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="开始时间" name="start_time">
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="结束时间" name="end_time">
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
          </Row>
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              onClick={() => {
                searchForm.resetFields();
                setSearchParams({});
                setSearchModal({ visible: false });
              }}
              className="rounded-md px-6"
            >
              重置
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md px-6"
            >
              查询
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center space-x-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">编辑任务</span>
          </div>
        }
        width={600}
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button onClick={handleDrawerClose} className="rounded-md px-6">
              取消
            </Button>
            <Button
              type="primary"
              loading={editDrawer.loading}
              onClick={() => editForm.submit()}
              className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md px-6"
            >
              保存
            </Button>
          </div>
        }
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditSubmit}>
          <Form.Item
            label="任务名称"
            name="name"
            rules={[{ required: true, message: "请输入任务名称" }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item
            label="任务分组"
            name="group"
            rules={[{ required: true, message: "请选择任务分组" }]}
          >
            <Select placeholder="请选择任务分组">
              <Option value="系统维护">系统维护</Option>
              <Option value="数据备份">数据备份</Option>
              <Option value="监控告警">监控告警</Option>
              <Option value="日志清理">日志清理</Option>
              <Option value="性能优化">性能优化</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="开始时间"
                name="start_time"
                rules={[{ required: true, message: "请输入开始时间" }]}
              >
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="结束时间"
                name="end_time"
                rules={[{ required: true, message: "请输入结束时间" }]}
              >
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="星期"
                name="weekday"
                rules={[{ required: true, message: "请选择星期" }]}
              >
                <Select placeholder="请选择星期">
                  {WEEKDAY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="执行频率"
                name="frequency"
                rules={[{ required: true, message: "请选择执行频率" }]}
              >
                <Select placeholder="请选择执行频率">
                  {FREQUENCY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="重试次数"
                name="retryNum"
                rules={[{ required: true, message: "请输入重试次数" }]}
              >
                <Input placeholder="请输入重试次数" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="重试间隔"
                name="retry_frequency"
                rules={[{ required: true, message: "请输入重试间隔" }]}
              >
                <Input placeholder="请输入重试间隔" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="告警触发条件"
            name="alert_trigger"
            rules={[{ required: true, message: "请输入告警触发条件" }]}
          >
            <Select placeholder="请选择告警触发条件">
              <Option value="失败时">失败时</Option>
              <Option value="成功时">成功时</Option>
              <Option value="总是">总是</Option>
              <Option value="从不">从不</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="告警接收人"
            name="alert_receiver"
            rules={[{ required: true, message: "请输入告警接收人" }]}
          >
            <Input placeholder="请输入告警接收人" />
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {TASK_STATUS_OPTIONS.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default AntdTable;
